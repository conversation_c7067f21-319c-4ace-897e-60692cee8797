# State-Based Pipeline Architecture Documentation

## **1. 📝 Feature Idea / Problem Statement**

**Description**: Modern businesses require reliable, scalable voice AI systems that can handle complex conversational workflows while maintaining consistent user experiences. The state-based pipeline architecture solves critical challenges in voice agent orchestration, ensuring robust processing of speech-to-text, natural language understanding, business logic execution, and text-to-speech conversion.

**Context**: Voice AI systems face unique challenges that traditional architectures struggle to address:
- **Latency Sensitivity**: Users expect near-instantaneous responses, requiring efficient pipeline orchestration
- **Multi-Modal Processing**: Voice interactions involve audio processing, text analysis, business logic, and audio synthesis
- **Error Recovery**: Network failures, API timeouts, and processing errors must be handled gracefully without breaking user experience
- **State Management**: Complex conversations require maintaining context across multiple processing steps
- **Quality Assurance**: Each processing step must validate inputs/outputs to prevent cascading failures

**Summary**: A state-based pipeline architecture that orchestrates specialized voice processing agents through a centralized StateManager, providing reliable workflow execution, comprehensive error handling, and consistent user experiences across voice interactions.

**Target Audience**:
- **Business Users**: Customer service teams, call center operators, and end-users requiring reliable voice AI interactions
- **Technical Teams**: Voice AI developers, system architects, and DevOps teams building conversational platforms
- **Product Managers**: Teams responsible for voice AI product quality and user experience metrics

## **2. 🥅 Key Goals & Benefits**

**Goals**:
- **Reliable Voice Processing**: Ensure consistent, high-quality voice interactions across all user touchpoints
- **Scalable Architecture**: Support growing user bases and complex conversational workflows
- **Robust Error Recovery**: Handle network failures, API timeouts, and processing errors gracefully
- **Quality Assurance**: Validate data integrity at each processing step to prevent cascading failures
- **Performance Optimization**: Minimize latency while maintaining processing accuracy

**Business Benefits**:
- **Enhanced User Experience**: Consistent, reliable voice interactions increase user satisfaction and engagement
- **Reduced Support Costs**: Robust error handling minimizes failed interactions requiring human intervention
- **Improved Scalability**: Modular architecture supports business growth without major system redesigns
- **Quality Assurance**: Input/output validation prevents poor user experiences from malformed data
- **Operational Reliability**: Centralized state management enables better monitoring and troubleshooting

**Technical Benefits**:
- **Maintainable Codebase**: Clear separation between orchestration and business logic
- **Easy Integration**: Standardized interfaces simplify adding new voice processing capabilities
- **Comprehensive Testing**: Isolated components enable thorough unit and integration testing
- **Performance Monitoring**: Built-in latency tracking and metrics collection
- **Fault Tolerance**: Multi-layer error handling with graceful degradation

## **3. 📖 Research & Architecture Evolution**

**Market Research**: State-based architectures are widely used in workflow engines and business process management systems for their reliability and maintainability.

**User Feedback**: Development teams prefer clear separation between orchestration logic and business logic for easier debugging and maintenance.

### Key Architectural Changes

**Previous Agent-Based Approach:**
- Agents acted independently and managed their own state
- Complex inter-agent communication patterns
- Tight coupling between agents
- Difficult to manage execution order and error handling

**New State-Based Approach:**
- States orchestrate agents through a standardized interface
- StateManager controls workflow execution and transitions
- Clear separation between orchestration (states) and implementation (agents)
- Centralized validation and error handling at state level
- Agents focus purely on their core functionality

**Benefits of the Change:**
- **Better Control Flow**: StateManager has complete visibility and control
- **Easier Testing**: States can be tested independently with mock agents
- **Improved Maintainability**: Clear boundaries between orchestration and business logic
- **Enhanced Reliability**: Centralized error handling and validation

## **4. 🏗️ Solution (How it should work)**

**Proposed Solution**:
A state-based pipeline architecture that provides reliable, scalable voice processing through centralized orchestration:

- **StateManager**: Central orchestrator that manages workflow execution and ensures proper state transitions
- **Pipeline States**: Specialized orchestrators for each processing step (STT, preprocessing, processing, TTS)
- **Processing Agents**: Domain-specific implementations that handle the actual voice processing work
- **Validation Layer**: Pydantic schemas ensure data integrity throughout the pipeline
- **Error Recovery**: Multi-level fallback mechanisms maintain service availability

**User Journey**:
1. **User speaks** → Audio captured and sent to voice processing pipeline
2. **Speech Recognition** → Audio converted to text with confidence scoring
3. **Text Analysis** → Intent detection, emotion analysis, and text cleaning
4. **Business Processing** → Route to appropriate business logic and generate response
5. **Speech Synthesis** → Convert response to natural-sounding audio
6. **User receives response** → High-quality audio delivered with minimal latency

**Business Value Flow**:
- **Consistent Quality**: Every interaction follows the same validated processing pipeline
- **Reliable Service**: Error handling ensures users always receive a response
- **Scalable Operations**: Modular design supports growing conversation complexity
- **Performance Monitoring**: Built-in metrics enable continuous optimization

## **5. 🧑🏻‍💻 Technical Implementation**

### 📋 Entities Involved

#### Core Orchestration Components

**StateManager**
- Orchestrates workflow execution and state transitions
- Manages agent registry and memory manager
- Loads workflow and Layer2 configurations
- Handles session lifecycle and error recovery

**AbstractPipelineState**
- Base class for all pipeline states with standard interface
- Provides input/output validation using Pydantic schemas
- Handles error management and notification publishing
- Manages agent retrieval and execution

**Agent Registry**
- Manages agent instances and provides lookup functionality
- Agents are retrieved by states for execution
- Supports dynamic agent registration and health monitoring

#### Pipeline States

**STTState**: Orchestrates speech-to-text conversion
- Validates audio input format and size
- Executes STTAgent with retry logic
- Publishes transcript and latency metrics

**PreProcessingState**: Orchestrates text cleaning and analysis
- Validates transcript input
- Executes preprocessing pipeline for intent, emotion, and gender detection
- Publishes cleaned text and analysis results

**ProcessingState**: Orchestrates business logic and LLM processing
- Validates clean text and intent
- Routes to appropriate business logic (internal, database, or RAG)
- Generates LLM responses and updates conversation memory

**FillerState**: Orchestrates filler audio generation
- Manages conversation flow during processing delays
- Generates contextual filler phrases
- Provides seamless user experience during long operations

**TTSState**: Orchestrates text-to-speech conversion
- Validates text input and voice parameters
- Executes TTS synthesis with emotion and gender configuration
- Publishes audio output and performance metrics

#### Specialized Processing Agents

**STTAgent (Speech-to-Text Agent)**
- **Purpose**: Converts audio input to text transcripts using OpenAI Whisper-1 model
- **Input**: Audio bytes or file path (supports multiple formats)
- **Output**: Transcribed text with confidence scores and latency metrics
- **Features**:
  - Multi-language support (Arabic, English, etc.)
  - Retry logic with exponential backoff (3 attempts)
  - Temporary file management for audio processing
  - LLM token usage tracking for cost monitoring
  - Redis context management for session state
- **Error Handling**: Graceful fallback with error notifications to orchestrator
- **Performance**: Tracks processing latency and publishes completion notifications

**PreprocessingAgent (Text Analysis Agent)**
- **Purpose**: Cleans text and extracts intent, emotion, and gender using LLM analysis
- **Input**: Raw transcript text from STT processing
- **Output**: Clean text, detected intent, emotion (optional), gender (optional)
- **Features**:
  - Text normalization and cleaning
  - Intent classification using GPT-4o-mini
  - Emotion detection with configurable toggle
  - Gender detection with configurable toggle
  - Disambiguation support (configurable)
  - LLM call tracking for token usage monitoring
- **Configuration**: Feature toggles for emotion/gender detection
- **Error Handling**: Retry logic with fallback responses
- **Performance**: Latency tracking and Redis context updates

**ProcessingAgent (Business Logic Agent)**
- **Purpose**: Routes requests and generates responses using business logic and LLM processing
- **Input**: Clean text and detected intent from preprocessing
- **Output**: LLM-generated response and business-specific data
- **Features**:
  - Multi-route processing (internal logic, database, RAG engine)
  - Conversation history management in contextual memory
  - Persistent memory access for long-term user data
  - LLM post-processing for response generation
  - Business rule execution based on intent
- **Routing Logic**:
  - Account-related queries → Internal logic processing
  - Database queries → Database route with data fetching
  - Knowledge queries → RAG engine integration
- **Error Handling**: Redis fallback scenarios with graceful degradation
- **Memory Integration**: Updates conversation history and persistent user data

**TTSAgent (Text-to-Speech Agent)**
- **Purpose**: Converts text to speech using multiple provider options
- **Input**: Text message with voice configuration (emotion, gender)
- **Output**: Audio file path and synthesis latency metrics
- **Provider Support**:
  - **Google TTS**: Primary provider with emotion/gender voice selection
  - **OpenAI TTS**: Alternative provider with high-quality voices
  - **ElevenLabs**: Premium voice synthesis option
- **Features**:
  - Voice parameter configuration (emotion: neutral/happy/sad, gender: male/female)
  - Retry logic with exponential backoff (3 attempts)
  - Fallback audio generation when providers fail
  - Audio file management and cleanup
  - LLM token usage tracking for cost monitoring
- **Error Handling**: Multi-provider fallback with synthesized backup audio
- **Performance**: Latency tracking and Redis context updates

**FillerTTSAgent (Conversation Flow Agent)**
- **Purpose**: Generates filler audio during processing delays to maintain conversation flow
- **Input**: Optional filler text or uses predefined phrases
- **Output**: Filler audio file path and generation metrics
- **Features**:
  - Predefined filler phrase library ("Just a moment...", "Let me check...")
  - Leverages main TTSAgent for audio synthesis
  - Context-aware filler selection
  - Quick audio generation for minimal delay
- **Use Cases**: Long LLM processing, database queries, RAG engine searches
- **Error Handling**: Graceful fallback with default phrases
- **Performance**: Optimized for low-latency filler generation

#### Supporting Components

**Pydantic Schemas**
- Define input/output validation for each state
- Ensure type safety and data integrity across pipeline
- Support for optional fields and default values
- Comprehensive validation error reporting

### 🏗️ Architecture Overview

```mermaid
flowchart TD
    subgraph StateManager
        SM[StateManager]
        AR[Agent Registry]
        MM[Memory Manager]
    end

    subgraph States
        STT[STTState]
        PP[PreProcessingState] 
        P[ProcessingState]
        F[FillerState]
        TTS[TTSState]
    end

    subgraph Agents
        STTA[STTAgent]
        PPA[PreprocessingAgent]
        PA[ProcessingAgent]
        FA[FillerTTSAgent]
        TTSA[TTSAgent]
    end

    SM -->|orchestrates| STT
    SM -->|orchestrates| PP
    SM -->|orchestrates| P
    SM -->|orchestrates| F
    SM -->|orchestrates| TTS

    STT -->|retrieves & executes| STTA
    PP -->|retrieves & executes| PPA
    P -->|retrieves & executes| PA
    F -->|retrieves & executes| FA
    TTS -->|retrieves & executes| TTSA

    AR -->|provides agents| STT
    AR -->|provides agents| PP
    AR -->|provides agents| P
    AR -->|provides agents| F
    AR -->|provides agents| TTS

    MM -->|shared context| States
```

### 🔄 Data Flow

1. **StateManager.executePipelineState()** called with input data
2. **State validates input** using Pydantic schema
3. **State retrieves agent** from agent registry
4. **State executes agent.process()** with validated input
5. **State validates output** using Pydantic schema
6. **State publishes notification** via Redis pub/sub
7. **StateManager transitions** to next state based on workflow

### 🛠️ State Implementation Pattern

Each state follows this standardized pattern:

```python
class ExampleState(AbstractPipelineState):
    input_schema_class = ExampleInputSchema
    output_schema_class = ExampleOutputSchema

    async def process(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> StateOutput:
        # 1. Validate input
        validated_input = self.validate_input(input_data)
        
        # 2. Get agent from registry
        agent = self.agent_registry.getAgent("example_agent")
        
        # 3. Execute agent
        result = await agent.process(validated_input.model_dump(), context)
        
        # 4. Validate output
        validated_output = self.validate_output(result.outputs)
        
        # 5. Publish notification
        await self._publish_notification("complete", {...})
        
        return result
```

## **6. 🤝🏻 Integration with Other Parts of the System**

**Usage**: States are executed by StateManager through the `executePipelineState()` method. Each state is responsible for:
- Input validation using Pydantic schemas
- Agent retrieval and execution
- Output validation and error handling
- Notification publishing for orchestration

**Dependencies**: 
- Agent Registry for agent lookup
- Memory Manager for context sharing
- Redis for pub/sub notifications
- Pydantic schemas for validation

**APIs**: States expose a standard `process()` method that accepts input data and context, returning a `StateOutput` object with status, message, outputs, and metadata.

### 💡 Practical Example: Adding a New State

To add a new state to the pipeline, follow these steps:

**1. Define Pydantic Schemas**
```python
# In schemas/layer2_schema.py
class NewStateInputSchema(BaseModel):
    input_text: str = Field(..., description="Text to process")

class NewStateOutputSchema(BaseModel):
    processed_text: str = Field(..., description="Processed result")
    latency: int = Field(..., description="Processing time in ms")
```

**2. Implement the State Class**
```python
# In core/state_manager/state_output.py
class NewState(AbstractPipelineState):
    input_schema_class = NewStateInputSchema
    output_schema_class = NewStateOutputSchema

    async def process(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> StateOutput:
        try:
            validated_input = self.validate_input(input_data)
            agent = self.agent_registry.getAgent("new_agent")
            result = await agent.process(validated_input.model_dump(), context)
            validated_output = self.validate_output(result.outputs)
            result.outputs = validated_output.model_dump()
            await self._publish_notification("complete", {"latency": result.outputs.get("latency")}, ["processed_text", "latency"])
            return result
        except Exception as e:
            await self._publish_notification("error", {}, error_message=str(e))
            return StateOutput(status=StatusType.ERROR, message=f"NewState error: {str(e)}", ...)
```

**3. Register in StateManager**
```python
# In core/state_manager/state_manager.py
self.pipeline_state_map = {
    "stt_process": STTState,
    "preprocessing_process": PreProcessingState,
    "processing_process": ProcessingState,
    "filler_tts_process": FillerState,
    "tts_process": TTSState,
    "new_process": NewState,  # Add your new state
}
```

## **7. 🧪 Test Cases**

### Unit Tests for Abstract State

**Test Input Validation**
- Valid input passes validation
- Invalid input raises ValidationError
- Missing required fields are caught

**Test Output Validation**
- Valid output passes validation
- Invalid output logs warning but continues
- Schema validation errors are handled gracefully

**Test Error Handling**
- Agent execution errors return ERROR status
- Validation errors return VALIDATION_ERROR status
- Notifications are published on both success and error

### Integration Tests for Concrete States

**STTState Tests**
- Audio input is processed correctly
- Transcript and latency are returned
- Completion notification is published

**PreProcessingState Tests**
- Text cleaning and intent detection work
- Emotion and gender detection are optional
- All preprocessing outputs are validated

**ProcessingState Tests**
- Business logic execution succeeds
- LLM responses are generated correctly
- Memory context is updated properly

**TTSState Tests**
- Text-to-speech conversion works
- Audio file path is returned
- Voice parameters (emotion/gender) are applied

### End-to-End Workflow Tests

**Complete Pipeline Execution**
- StateManager orchestrates full workflow
- State transitions happen correctly
- Memory context is maintained across states
- Error recovery works at each stage

**Validation Integration**
- Input/output schemas work across state boundaries
- Data flows correctly between states
- Type safety is maintained throughout pipeline
