# State-Based Pipeline Architecture Documentation

## **1. 📝 Feature Idea / Problem Statement**

**Description**: The voice agent platform requires a robust, scalable architecture where workflow states orchestrate specialized agents rather than agents acting independently. This state-based approach provides better control flow, validation, and error handling.

**Context**: Traditional agent-based architectures can lead to tight coupling and complex inter-agent communication. By implementing states that orchestrate agents, we achieve cleaner separation of concerns and more predictable execution flows.

**Summary**: A hierarchical state management system where `StateManager` orchestrates workflow states, and each state executes specialized agents through a standardized interface with input/output validation.

**Target Audience**: Voice agent developers, system architects, and integration teams building conversational AI platforms.

## **2. 🥅 Key Goals & Benefits**

**Goals**:
- Implement state-based pipeline architecture where states orchestrate agents
- Provide standardized input/output validation using Pydantic schemas
- Enable workflow-driven execution with clear state transitions
- Maintain separation between orchestration logic and agent implementation

**Benefits**:
- **Better Control Flow**: States manage execution order and transitions
- **Input/Output Validation**: Pydantic schemas ensure data integrity
- **Error Handling**: Centralized error management at state level
- **Scalability**: Easy to add new states and agents without affecting existing components
- **Testability**: Clear interfaces make unit testing straightforward

## **3. 📖 Research & Architecture Evolution**

**Market Research**: State-based architectures are widely used in workflow engines and business process management systems for their reliability and maintainability.

**User Feedback**: Development teams prefer clear separation between orchestration logic and business logic for easier debugging and maintenance.

### Key Architectural Changes

**Previous Agent-Based Approach:**
- Agents acted independently and managed their own state
- Complex inter-agent communication patterns
- Tight coupling between agents
- Difficult to manage execution order and error handling

**New State-Based Approach:**
- States orchestrate agents through a standardized interface
- StateManager controls workflow execution and transitions
- Clear separation between orchestration (states) and implementation (agents)
- Centralized validation and error handling at state level
- Agents focus purely on their core functionality

**Benefits of the Change:**
- **Better Control Flow**: StateManager has complete visibility and control
- **Easier Testing**: States can be tested independently with mock agents
- **Improved Maintainability**: Clear boundaries between orchestration and business logic
- **Enhanced Reliability**: Centralized error handling and validation

## **4. 🏗️ Solution (How it should work)**

**Proposed Solution**: 
- `StateManager` orchestrates workflow execution
- Abstract `AbstractPipelineState` base class defines standard interface
- Concrete state classes (STTState, PreProcessingState, etc.) implement specific logic
- Each state retrieves its agent from the agent registry and executes it
- States handle validation, error handling, and notification publishing

**User Journey**:
1. User provides audio input
2. StateManager executes current workflow state
3. State validates input, executes agent, validates output
4. State publishes completion notification
5. StateManager transitions to next state based on workflow definition

## **5. 🧑🏻‍💻 Technical Implementation**

### 📋 Entities Involved

**StateManager**
- Orchestrates workflow execution and state transitions
- Manages agent registry and memory manager
- Loads workflow and Layer2 configurations

**AbstractPipelineState**
- Base class for all pipeline states with standard interface
- Provides input/output validation using Pydantic schemas
- Handles error management and notification publishing

**Concrete States**
- **STTState**: Orchestrates speech-to-text conversion
- **PreProcessingState**: Orchestrates text cleaning and analysis
- **ProcessingState**: Orchestrates business logic and LLM processing
- **FillerState**: Orchestrates filler audio generation
- **TTSState**: Orchestrates text-to-speech conversion

**Agent Registry**
- Manages agent instances and provides lookup functionality
- Agents are retrieved by states for execution

**Pydantic Schemas**
- Define input/output validation for each state
- Ensure type safety and data integrity

### 🏗️ Architecture Overview

```mermaid
flowchart TD
    subgraph StateManager
        SM[StateManager]
        AR[Agent Registry]
        MM[Memory Manager]
    end

    subgraph States
        STT[STTState]
        PP[PreProcessingState] 
        P[ProcessingState]
        F[FillerState]
        TTS[TTSState]
    end

    subgraph Agents
        STTA[STTAgent]
        PPA[PreprocessingAgent]
        PA[ProcessingAgent]
        FA[FillerTTSAgent]
        TTSA[TTSAgent]
    end

    SM -->|orchestrates| STT
    SM -->|orchestrates| PP
    SM -->|orchestrates| P
    SM -->|orchestrates| F
    SM -->|orchestrates| TTS

    STT -->|retrieves & executes| STTA
    PP -->|retrieves & executes| PPA
    P -->|retrieves & executes| PA
    F -->|retrieves & executes| FA
    TTS -->|retrieves & executes| TTSA

    AR -->|provides agents| STT
    AR -->|provides agents| PP
    AR -->|provides agents| P
    AR -->|provides agents| F
    AR -->|provides agents| TTS

    MM -->|shared context| States
```

### 🔄 Data Flow

1. **StateManager.executePipelineState()** called with input data
2. **State validates input** using Pydantic schema
3. **State retrieves agent** from agent registry
4. **State executes agent.process()** with validated input
5. **State validates output** using Pydantic schema
6. **State publishes notification** via Redis pub/sub
7. **StateManager transitions** to next state based on workflow

### 🛠️ State Implementation Pattern

Each state follows this standardized pattern:

```python
class ExampleState(AbstractPipelineState):
    input_schema_class = ExampleInputSchema
    output_schema_class = ExampleOutputSchema

    async def process(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> StateOutput:
        # 1. Validate input
        validated_input = self.validate_input(input_data)
        
        # 2. Get agent from registry
        agent = self.agent_registry.getAgent("example_agent")
        
        # 3. Execute agent
        result = await agent.process(validated_input.model_dump(), context)
        
        # 4. Validate output
        validated_output = self.validate_output(result.outputs)
        
        # 5. Publish notification
        await self._publish_notification("complete", {...})
        
        return result
```

## **6. 🤝🏻 Integration with Other Parts of the System**

**Usage**: States are executed by StateManager through the `executePipelineState()` method. Each state is responsible for:
- Input validation using Pydantic schemas
- Agent retrieval and execution
- Output validation and error handling
- Notification publishing for orchestration

**Dependencies**: 
- Agent Registry for agent lookup
- Memory Manager for context sharing
- Redis for pub/sub notifications
- Pydantic schemas for validation

**APIs**: States expose a standard `process()` method that accepts input data and context, returning a `StateOutput` object with status, message, outputs, and metadata.

### 💡 Practical Example: Adding a New State

To add a new state to the pipeline, follow these steps:

**1. Define Pydantic Schemas**
```python
# In schemas/layer2_schema.py
class NewStateInputSchema(BaseModel):
    input_text: str = Field(..., description="Text to process")

class NewStateOutputSchema(BaseModel):
    processed_text: str = Field(..., description="Processed result")
    latency: int = Field(..., description="Processing time in ms")
```

**2. Implement the State Class**
```python
# In core/state_manager/state_output.py
class NewState(AbstractPipelineState):
    input_schema_class = NewStateInputSchema
    output_schema_class = NewStateOutputSchema

    async def process(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> StateOutput:
        try:
            validated_input = self.validate_input(input_data)
            agent = self.agent_registry.getAgent("new_agent")
            result = await agent.process(validated_input.model_dump(), context)
            validated_output = self.validate_output(result.outputs)
            result.outputs = validated_output.model_dump()
            await self._publish_notification("complete", {"latency": result.outputs.get("latency")}, ["processed_text", "latency"])
            return result
        except Exception as e:
            await self._publish_notification("error", {}, error_message=str(e))
            return StateOutput(status=StatusType.ERROR, message=f"NewState error: {str(e)}", ...)
```

**3. Register in StateManager**
```python
# In core/state_manager/state_manager.py
self.pipeline_state_map = {
    "stt_process": STTState,
    "preprocessing_process": PreProcessingState,
    "processing_process": ProcessingState,
    "filler_tts_process": FillerState,
    "tts_process": TTSState,
    "new_process": NewState,  # Add your new state
}
```

## **7. 🧪 Test Cases**

### Unit Tests for Abstract State

**Test Input Validation**
- Valid input passes validation
- Invalid input raises ValidationError
- Missing required fields are caught

**Test Output Validation**
- Valid output passes validation
- Invalid output logs warning but continues
- Schema validation errors are handled gracefully

**Test Error Handling**
- Agent execution errors return ERROR status
- Validation errors return VALIDATION_ERROR status
- Notifications are published on both success and error

### Integration Tests for Concrete States

**STTState Tests**
- Audio input is processed correctly
- Transcript and latency are returned
- Completion notification is published

**PreProcessingState Tests**
- Text cleaning and intent detection work
- Emotion and gender detection are optional
- All preprocessing outputs are validated

**ProcessingState Tests**
- Business logic execution succeeds
- LLM responses are generated correctly
- Memory context is updated properly

**TTSState Tests**
- Text-to-speech conversion works
- Audio file path is returned
- Voice parameters (emotion/gender) are applied

### End-to-End Workflow Tests

**Complete Pipeline Execution**
- StateManager orchestrates full workflow
- State transitions happen correctly
- Memory context is maintained across states
- Error recovery works at each stage

**Validation Integration**
- Input/output schemas work across state boundaries
- Data flows correctly between states
- Type safety is maintained throughout pipeline
